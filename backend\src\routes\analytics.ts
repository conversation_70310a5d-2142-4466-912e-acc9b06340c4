import { Router } from 'express';
import { getAnalytics, getUserAnalytics } from '../controllers/analyticsController';
import { authenticate, authorize } from '../middleware/auth';

const router = Router();

// All analytics routes require authentication
router.use(authenticate);

// GET /api/analytics - Only WorkFlowManagement and managers can view system analytics
router.get('/', authorize('WorkFlowManagement', 'Manager', 'Developer'), getAnalytics);

// GET /api/analytics/user/:userId
router.get('/user/:userId', getUserAnalytics);

export default router;
