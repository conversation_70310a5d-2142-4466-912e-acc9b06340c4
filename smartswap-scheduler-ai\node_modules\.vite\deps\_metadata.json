{"hash": "6554e31a", "configHash": "6800cedc", "lockfileHash": "d9a4c3dd", "browserHash": "0dbc6aba", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "6245dbda", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "937d2a06", "needsInterop": true}, "react-router-dom": {"src": "../../../../node_modules/react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "12d22fe3", "needsInterop": false}, "@tanstack/react-query": {"src": "../../../../node_modules/@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "86c81e7e", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "c8e9ef7e", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "3583d359", "needsInterop": true}, "@radix-ui/react-avatar": {"src": "../../../../node_modules/@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "c2613b57", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../../../node_modules/@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "183b920f", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../../../node_modules/@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "0dd710a1", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "e8040917", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../../../node_modules/@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "7b4cf598", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../../../node_modules/@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "8d263a44", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "6ac2b020", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../../../node_modules/@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "2eca9e76", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../../../node_modules/@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "69e812ee", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../../../node_modules/@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "cd2d3c4c", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../../../node_modules/@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "14bb67f2", "needsInterop": false}, "class-variance-authority": {"src": "../../../../node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "6a963219", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "0d81eca3", "needsInterop": false}, "next-themes": {"src": "../../../../node_modules/next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "5b8a2fbc", "needsInterop": false}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "35ef20a2", "needsInterop": true}, "react-hook-form": {"src": "../../../../node_modules/react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "c39a9901", "needsInterop": false}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "df147d70", "needsInterop": true}, "recharts": {"src": "../../../../node_modules/recharts/es6/index.js", "file": "recharts.js", "fileHash": "23700021", "needsInterop": false}, "sonner": {"src": "../../../../node_modules/sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "45211ab4", "needsInterop": false}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "59ac713b", "needsInterop": false}}, "chunks": {"chunk-MKYRKYXK": {"file": "chunk-MKYRKYXK.js"}, "chunk-3UPZO3IK": {"file": "chunk-3UPZO3IK.js"}, "chunk-YPDBXV73": {"file": "chunk-YPDBXV73.js"}, "chunk-TC4CW53X": {"file": "chunk-TC4CW53X.js"}, "chunk-RWF7UEWW": {"file": "chunk-RWF7UEWW.js"}, "chunk-WFXZHWCA": {"file": "chunk-WFXZHWCA.js"}, "chunk-RAUSWKG6": {"file": "chunk-RAUSWKG6.js"}, "chunk-CXQTFHLL": {"file": "chunk-CXQTFHLL.js"}, "chunk-EMSPKVCK": {"file": "chunk-EMSPKVCK.js"}, "chunk-SXX54OC7": {"file": "chunk-SXX54OC7.js"}, "chunk-GXD7YJGF": {"file": "chunk-GXD7YJGF.js"}, "chunk-K6YFPJWF": {"file": "chunk-K6YFPJWF.js"}, "chunk-NB24HQGR": {"file": "chunk-NB24HQGR.js"}, "chunk-IBI677K4": {"file": "chunk-IBI677K4.js"}, "chunk-6AOXWRVX": {"file": "chunk-6AOXWRVX.js"}, "chunk-X3Q7MIOI": {"file": "chunk-X3Q7MIOI.js"}, "chunk-7QGA4JVC": {"file": "chunk-7QGA4JVC.js"}, "chunk-NT5JDPQU": {"file": "chunk-NT5JDPQU.js"}, "chunk-JYSI5OBP": {"file": "chunk-JYSI5OBP.js"}, "chunk-7URR3GLA": {"file": "chunk-7URR3GLA.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}