import { Request, Response } from 'express';
import { User, Shift, SwapRequest } from '../models';
import { logger } from '../utils/logger';
import { AnalyticsData } from '../types';

export const getAnalytics = async (req: Request, res: Response) => {
  try {
    // Get basic system metrics
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ 
      updatedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // Last 30 days
    });
    
    const totalSwapRequests = await SwapRequest.countDocuments();
    const acceptedSwaps = await SwapRequest.countDocuments({ status: 'accepted' });
    const swapSuccessRate = totalSwapRequests > 0 ? (acceptedSwaps / totalSwapRequests) * 100 : 0;
    
    // Get skill distribution
    const skillDistribution = await User.aggregate([
      { $unwind: '$skills' },
      { $group: { _id: '$skills', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Get marketplace distribution
    const marketplaceData = await User.aggregate([
      { $group: { _id: '$marketplace', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Get swap trends (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const swapTrends = await SwapRequest.aggregate([
      { $match: { createdAt: { $gte: thirtyDaysAgo } } },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          count: { $sum: 1 },
          accepted: {
            $sum: { $cond: [{ $eq: ['$status', 'accepted'] }, 1, 0] }
          }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);
    
    const analyticsData: AnalyticsData = {
      swapTrends,
      skillDistribution,
      marketplaceData,
      systemMetrics: {
        activeUsers,
        swapSuccessRate: Math.round(swapSuccessRate * 100) / 100,
        avgMatchTime: 4.2, // Placeholder - would need more complex calculation
        systemEfficiency: 99.1 // Placeholder - would need more complex calculation
      }
    };
    
    res.json({
      success: true,
      data: analyticsData
    });
  } catch (error) {
    logger.error('Get analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting analytics'
    });
  }
};

export const getUserAnalytics = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    
    // Get user's swap history
    const userSwapRequests = await SwapRequest.countDocuments({
      $or: [{ requesterId: userId }, { targetUserId: userId }]
    });
    
    const userAcceptedSwaps = await SwapRequest.countDocuments({
      $or: [{ requesterId: userId }, { targetUserId: userId }],
      status: 'accepted'
    });
    
    const userSwapSuccessRate = userSwapRequests > 0 ? 
      (userAcceptedSwaps / userSwapRequests) * 100 : 0;
    
    // Get user's shift count
    const userShiftCount = await Shift.countDocuments({ userId });
    
    // Get user's recent activity
    const recentSwaps = await SwapRequest.find({
      $or: [{ requesterId: userId }, { targetUserId: userId }]
    })
    .sort({ createdAt: -1 })
    .limit(10)
    .populate('requesterId', 'firstName lastName')
    .populate('targetUserId', 'firstName lastName');
    
    const userAnalytics = {
      totalSwapRequests: userSwapRequests,
      acceptedSwaps: userAcceptedSwaps,
      swapSuccessRate: Math.round(userSwapSuccessRate * 100) / 100,
      totalShifts: userShiftCount,
      recentActivity: recentSwaps
    };
    
    res.json({
      success: true,
      data: userAnalytics
    });
  } catch (error) {
    logger.error('Get user analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting user analytics'
    });
  }
};
